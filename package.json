{"name": "zidio-e-commerce-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "node server/server.js", "client": "vite", "dev": "npx concurrently \"npm run start\" \"npm run client\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.6.1", "@tailwindcss/vite": "^4.0.14", "animejs": "^4.0.2", "axios": "^1.9.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "framer-motion": "^12.9.4", "lucide-react": "^0.488.0", "motion": "^12.9.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-charts": "^5.2.1", "react-loader-spinner": "^6.1.6", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-spinners": "^0.17.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.2", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.3.4"}}